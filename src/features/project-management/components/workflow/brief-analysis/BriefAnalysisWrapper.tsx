'use client';

import { useEffect, useState } from 'react';
import { useCoAgent, useCopilotChat } from '@copilotkit/react-core';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useCurrentStep, useWorkflowActions, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import type { EditorContentChanged, stateRouteAgent } from '@/shared/types/global';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME, MESSAGE_SEND_ROUTE_AGENT } from '@/shared/constants/global';
import { ENameStateAgentCopilotkit } from '@/shared/enums/global';
import BaseBriefAnalysis from './BaseBriefAnalysis';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { Role, TextMessage } from '@copilotkit/runtime-client-gql';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { useGetListTemplates } from '@/features/project-management/hooks/useProjectTemplate';
import type { TemplateFiles } from '@/features/project-management/types/project';
import { ETypeFile, ProjectCampaignEnum } from '@/features/project-management/types/project';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';

type BriefAnalysisResponse = {
  infos: { value: string }[];
};

const BriefAnalysisWrapper: React.FC = () => {
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync } = useUpdateStatusStep();

  const { appendMessage } = useCopilotChat();

  const workflow = useWorkflowTasks();

  const idSecondStep = workflow[1]?.steps[0]?.id;

  const { data: clientUploadData } = useGetInfoDetail<any, any>(idSecondStep ?? '');

  const [templateFile, setTemplateFile] = useState<TemplateFiles[]>([]);

  const [campainSelected, setCampainSelected] = useState<ProjectCampaignEnum>(ProjectCampaignEnum.IMC);

  const { data: templates } = useGetListTemplates();

  useEffect(() => {
    if (clientUploadData?.stepInfo.length && clientUploadData?.stepInfo[0]?.infos?.length) {
      setCampainSelected(clientUploadData?.stepInfo[0]?.infos[0]?.serviceOption);
    }
  }, [clientUploadData]);

  useEffect(() => {
    if (templates) {
      const templateSelect = templates.filter(template => template.campaign === campainSelected);
      let urlOptions: TemplateFiles[] = [];
      templateSelect.forEach(template => urlOptions = [...urlOptions, ...template.files]);
      setTemplateFile(urlOptions);
    }
  }, [templates, campainSelected]);

  const {
    completeStep,
    getNextStepId,
  } = useWorkflowActions();
  const currentStep = useCurrentStep();
  const currentStepId = currentStep?.id;

  const { data: briefAnalysis } = useGetInfoDetail<BriefAnalysisResponse, any>(currentStep?.id ?? '');

  const { state, setState } = useCoAgent<stateRouteAgent<BriefAnalysisFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
      agent_name: AGENT_NAME_COPILOTKIT.ANALYSIS,
    },
  });

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setForm(data);
      setIsLoading(false);
    };
    updateState();
  };

  useEffect(() => {
    if (briefAnalysis && briefAnalysis?.stepInfo.length) {
      const markdown = briefAnalysis.stepInfo[0]?.infos[0]?.value;

      updateMarkdownToState(markdown ?? '');
    }
  }, [briefAnalysis]);

  useEffect(() => {
    const briefAnalysisState = state[ENameStateAgentCopilotkit.ANALYSIS];
    console.log('answer_brief', briefAnalysisState);
    if (briefAnalysisState && briefAnalysisState.answer_brief && briefAnalysisState.answer_brief_process && briefAnalysisState.answer_brief_process === 'done') {
      updateMarkdownToState(briefAnalysisState.answer_brief);
    }
  }, [state]);

  const toggleEditMode = () => {
    setIsEditMode(prev => !prev);
  };

  const sendBriefAnswer = () => {
    setState(prev => ({
      ...prev,
      agent_name: AGENT_NAME_COPILOTKIT.ANALYSIS,
      [ENameStateAgentCopilotkit.ANALYSIS]: {
        answer_brief: markdown,
        ...templateFile.reduce((result, template) => {
          if (template.type === ETypeFile.BRIEF_TEMPLATE) {
            result.template_brief_url = [
              ...(result.template_brief_url || []),
              ...getFile([template.file]),
            ];
          }
          if (template.type === ETypeFile.BRIEF_QUESTION) {
            result.question_brief_url = [
              ...(result.question_brief_url || []),
              ...getFile([template.file]),
            ];
          }
          return result;
        }, {} as any),
      },
    }));

    appendMessage(
      new TextMessage({
        content: MESSAGE_SEND_ROUTE_AGENT,
        role: Role.Developer,
      }),
    );
  };

  const compareMarkdown = () => {
    const markdownInitial = briefAnalysis?.stepInfo[0]?.infos[0]?.value ?? '';
    const markdownCurrent = markdown;

    return markdownInitial === markdownCurrent;
  };

  const handleApprove = async () => {
    if (!currentStepId) {
      return;
    }

    setIsEditMode(false);

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markdown }],
        },
      ],
    };
    const nextStepId = getNextStepId();
    console.log(nextStepId);
    const isChanged = compareMarkdown();

    await updateQuestionAnswer(payload, currentStepId);

    if (currentStep.status !== EStatusTask.COMPLETED || (!isChanged && currentStep.status === EStatusTask.COMPLETED)) {
      sendBriefAnswer();
    }
    if (currentStep.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }
    completeStep(currentStepId);
  };

  const handleEditorChange = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);
  };

  const discardChange = () => {
    setForm(markdown);
    setIsEditMode(false);
  };

  const confirmChange = () => {
    setMarkdown(form);
    setIsEditMode(false);
  };

  return (
    <BaseBriefAnalysis
      isLoading={isLoading}
      markdown={markdown}
      form={form}
      isEditMode={isEditMode}
      onEditToggle={toggleEditMode}
      onConfirmChange={confirmChange}
      onDiscardChange={discardChange}
      onEditorChange={handleEditorChange}
      onApprove={handleApprove}
    />
  );
};

export default BriefAnalysisWrapper;
