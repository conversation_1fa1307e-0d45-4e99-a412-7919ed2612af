import { Button } from '@/shared/components/ui/button';
import { ArrowDownTrayIcon, CheckBadgeIcon, FileEditIcon } from '@/shared/icons';
import Editor from '@/shared/components/ui/editor/editor';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import type { EditorContentChanged } from '@/shared/types/global';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';

type BaseBriefAnalysisProps = {
  isLoading: boolean;
  markdown: string;
  form: string;
  isEditMode: boolean;
  onEditToggle: () => void;
  onConfirmChange: () => void;
  onDiscardChange: () => void;
  onEditorChange: (data: EditorContentChanged) => void;
  onApprove: () => void;
};

const BaseBriefAnalysis: React.FC<BaseBriefAnalysisProps> = ({
  isLoading,
  markdown,
  form,
  isEditMode,
  onEditToggle,
  onConfirmChange,
  onDiscardChange,
  onEditorChange,
  onApprove,
}) => {
  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">Analyzing</div>
          <ProjectCardSkeleton />
        </div>
      )
    : (
        <div className="p-4 md:p-6">
          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6 z-1">
            {isEditMode
              ? (
                  <>
                    <Button type="button" variant="outline" onClick={onDiscardChange}>
                      Discard Change
                    </Button>
                    <Button type="button" onClick={onConfirmChange}>
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Confirm
                    </Button>
                  </>
                )
              : (
                  <>
                    <Button type="button" variant="outline" onClick={onEditToggle}>
                      <FileEditIcon className="h-5 w-5 " />
                      Edit
                    </Button>
                    <Button type="button" variant="outline">
                      <ArrowDownTrayIcon className="h-5 w-5 " />
                    </Button>
                    <Button type="button" onClick={onApprove}>
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Approve
                    </Button>
                  </>
                )}
          </div>

          <div className="mt-6">
            {isEditMode
              ? (
                  <Editor onChange={e => onEditorChange(e)} value={form} />
                )
              : (
                  <MarkdownRenderer content={markdown} />
                )}
          </div>
        </div>
      );
};

export default BaseBriefAnalysis;
