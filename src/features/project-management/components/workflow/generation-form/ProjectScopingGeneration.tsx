'use client';

import { <PERSON><PERSON> } from '@/shared/components/ui/button';
import { ArrowDownTrayIcon, CheckBadgeIcon, FileEditIcon } from '@/shared/icons';
import { useEffect, useState } from 'react';
import { useCoAgent } from '@copilotkit/react-core';
import type { scopeOfWorkFlow } from '@/features/project-management/types/agent';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import Editor from '@/shared/components/ui/editor/editor';
import type { EditorContentChanged, stateRouteAgent } from '@/shared/types/global';
import { useCurrentStep, useCurrentTask, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { ENameStateAgentCopilotkit } from '@/shared/enums/global';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { stepInfosMarkdownResponse } from '@/features/project-management/types/project';

const ProjectScopingGeneration: React.FC = () => {
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const currentStep = useCurrentStep();
  const currentStepId = currentStep?.id;

  const currentTask = useCurrentTask();

  const { mutateAsync } = useUpdateStatusStep();

  const { completeStep, updateStatus } = useWorkflowActions();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { state } = useCoAgent<stateRouteAgent<scopeOfWorkFlow>>({
    name: AGENT_ROUTE_NAME,
  });

  const { data: sowAnalysis } = useGetInfoDetail<stepInfosMarkdownResponse, stepInfosMarkdownResponse>(currentStepId ?? '');

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setForm(data);
      setIsLoading(false);
    };
    updateState();
  };

  useEffect(() => {
    if (sowAnalysis && sowAnalysis?.stepInfo.length) {
      const markdown = sowAnalysis.stepInfo[0]?.infos[0]?.value;

      updateMarkdownToState(markdown ?? '');
    }
  }, [sowAnalysis]);

  useEffect(() => {
    const scopeState = state[ENameStateAgentCopilotkit.SCOPE];
    if (scopeState?.sow_analysis_output && scopeState.sow_analysis_process && scopeState.sow_analysis_process === 'done') {
      updateMarkdownToState(scopeState?.sow_analysis_output);
    }
  }, [state]);

  const toggleEditMode = () => {
    setIsEditMode(true);
  };

  const handleSubmit = async () => {
    if (!currentStepId) {
      return;
    }

    setIsEditMode(false);

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markdown }],
        },
      ],
    };
    console.log(currentTask);
    if (currentStep.status !== EStatusTask.COMPLETED) {
      await updateQuestionAnswer(payload, currentStepId);
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
    }

    completeStep(currentStepId);
  };

  const handleChangeEditor = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);
  };

  const discardChange = () => {
    setForm(markdown);
    setIsEditMode(false);
  };

  const confirmChange = () => {
    setMarkdown(form);
    setIsEditMode(false);
  };

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">Analyzing</div>
          <ProjectCardSkeleton />

        </div>
      )
    : (
        <div className="p-4 md:p-6">

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6 z-1">
            {isEditMode
              ? (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={discardChange}
                    >
                      Discard Change
                    </Button>

                    <Button
                      type="button"
                      onClick={confirmChange}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Confirm
                    </Button>
                  </>
                )
              : (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={toggleEditMode}
                    >
                      <FileEditIcon className="h-5 w-5 " />
                      Edit
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5 " />
                    </Button>
                    <Button
                      type="button"
                      onClick={handleSubmit}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Approve
                    </Button>
                  </>
                )}

          </div>

          <div className="mt-6">
            {
              isEditMode
                ? <Editor onChange={handleChangeEditor} value={form} />

                : <MarkdownRenderer content={markdown} />
            }
          </div>
        </div>
      );
};

export default ProjectScopingGeneration;
